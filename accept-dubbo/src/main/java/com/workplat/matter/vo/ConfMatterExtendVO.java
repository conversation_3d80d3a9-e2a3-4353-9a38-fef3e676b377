package com.workplat.matter.vo;


import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> <PERSON>eng
 * @package com.workplat.matter.vo
 * @description 事项扩展信息VO
 * @date 2025/5/21 16:17
 */
@Data
@Schema(description = "事项扩展信息VO")
public class ConfMatterExtendVO {


    @Schema(description = "事项id")
    private String matterId;

    @Schema(description = "事项名称")
    private String matterName;

    @Schema(description = "事项编码")
    private String matterCode;

    @Schema(description = "流程名称")
    private String flowName;

    @Schema(description = "流程编码")
    private String flowCode;

    @Schema(description = "pc流程名称")
    private String pcFlowName;

    @Schema(description = "pc流程编码")
    private String pcFlowCode;

    @Schema(description = "信息提交后的告知内容")
    private String informAfterSubmit;

    @Schema(description = "推荐服务", example = "[{\"serveName\": \"xxx\",\"serveId\": \"xxx\"}, {\"serveName\": \"xxx\",\"serveId\": \"xxx\"}]")
    private List<JSONObject> serveInfoVos;

    @Schema(description = "知情同意内容",  example = "{\"title\":\"知情同意标题\",\"content\":\"内容xxxxxxxxxxxxxxxx\"}")
    private String informedConsent;

    @Schema(description = "申报须知（第三方）提示内容",  example = "{\"emptyTip\":\"信息为空提示。。。。。。。\",\"infoErrorTip\":\"信息有误提示。。。。。。。\"}")
    private String instructionRemoteTip;

}
