package com.workplat.matter.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.vo
 * @description
 * @date 2025/6/10 14:39
 */
@Data
@Schema(description = "网点窗口Request")
public class NetworkWindowRequest {

    @Schema(description = "网点名称")
    private String networkName;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "窗口信息")
    private String windowInfo;
}
