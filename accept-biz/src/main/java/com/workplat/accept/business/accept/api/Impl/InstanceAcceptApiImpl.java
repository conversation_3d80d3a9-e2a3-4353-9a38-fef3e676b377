package com.workplat.accept.business.accept.api.Impl;

import com.workplat.accept.business.accept.api.InstanceAcceptApi;
import com.workplat.gss.application.dubbo.constant.InstanceApplicationStatusEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.log.annotation.ApiLogging;
import com.workplat.gss.log.constant.OperationType;
import com.workplat.push.ga.service.GaDeclarePushService;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Odin
 * @Date: 2024/9/24 10:47
 * @Description:
 */

@RestController
public class InstanceAcceptApiImpl implements InstanceAcceptApi {

    private final BizInstanceInfoService bizInstanceInfoService;
    private final GaDeclarePushService gaDeclarePushService;

    public InstanceAcceptApiImpl(BizInstanceInfoService bizInstanceInfoService, GaDeclarePushService gaDeclarePushService) {
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.gaDeclarePushService = gaDeclarePushService;
    }


    @Override
    @ApiLogging(module = "业务审批", operation = "更新草稿", type = OperationType.UPDATE)
    public ResponseData<String> updateDraft(String instanceId) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(instanceId);
        if (bizInstanceInfo != null) {
            bizInstanceInfo.setApplicationStatusEnum(InstanceApplicationStatusEnum.TEMP);
            bizInstanceInfoService.update(bizInstanceInfo);
        }
        return ResponseData.success("更新成功");
    }
}
