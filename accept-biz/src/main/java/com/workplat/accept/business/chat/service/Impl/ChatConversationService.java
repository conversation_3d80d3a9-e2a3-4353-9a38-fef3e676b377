package com.workplat.accept.business.chat.service.Impl;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.convert.BizChatConversationVOConvert;
import com.workplat.accept.business.chat.dto.BizChatConversationDTO;
import com.workplat.accept.business.chat.dto.DeleteConversationDTO;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.service.BizChatConversationService;
import com.workplat.accept.business.chat.vo.BizChatConversationVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class ChatConversationService {

    private final BizChatConversationService bizChatConversationService;

    private final BizChatConversationVOConvert bizChatConversationVOConvert;


    public ChatConversationService(BizChatConversationService bizChatConversationService, BizChatConversationVOConvert bizChatConversationVOConvert) {
        this.bizChatConversationService = bizChatConversationService;
        this.bizChatConversationVOConvert = bizChatConversationVOConvert;
    }

    public BizChatConversation createConversation(String userId, String title, String channel, String instanceId) {
        BizChatConversation conversation = BizChatConversation.builder()
                .userId(userId)
                .title(title)
                .channel(channel)
                .instanceId(instanceId == null ? "-1" : instanceId)
                .build();
        return bizChatConversationService.createConversation(conversation);
    }

    public void updateConversation(BizChatConversation bizChatConversation) {
        bizChatConversationService.update(bizChatConversation);
    }


    public void deleteConversation(DeleteConversationDTO deleteConversationDTO) {
        String[] split = deleteConversationDTO.getConversationIds().split(",");
        bizChatConversationService.deleteByIds(List.of(split));
    }

    public Page<BizChatConversationVO> getConversationPage(BizChatConversationDTO dto, PageableDTO pageable) {
        // 只查询未初始化的会话
        dto.setIsInit(false);
        Map<String, Object> params = getParams(dto);
        Page<BizChatConversation> bizChatConversations =
                bizChatConversationService.queryForPage(params, pageable.convertPageable());
        return bizChatConversationVOConvert.convert(bizChatConversations);
    }

    private static Map<String, Object> getParams(BizChatConversationDTO dto) {
        Map<String, Object> params = MapUtil.<String, Object>builder().put("=(userId)", dto.getUserId()).put("=(channel)", dto.getChannel()).build();
        if (Boolean.FALSE.equals(dto.getIsInit())){
            params.put("=(instanceId)", "-1");
        }else {
            params.put("<>(instanceId)", "-1");
        }
        return params;
    }

    public BizChatConversationVO getLatestConversation(BizChatConversationDTO dto) {
        PageableDTO pageable = PageableDTO.builder().pageNo(1).pageSize(1).sort(new String[]{"updateTime"}).direction(new String[]{"DESC"}).build();
        Page<BizChatConversation> bizChatConversations =
                bizChatConversationService.queryForPage(getParams(dto), pageable.convertPageable());
        BizChatConversationVO first = bizChatConversationVOConvert.convert(bizChatConversations).getContent().getFirst();
        // 判断是否是近2小时的会话
        if (first != null && first.getUpdateTime().getTime() > System.currentTimeMillis() - 2 * 60 * 60 * 1000) {
            return first;
        }
        return null;
    }

    public BizChatConversation getConversation(String recordId) {
        return bizChatConversationService.queryForSingle(ImmutableMap.of("=(id)", recordId));
    }

    public void createConversation(BizChatConversation conversation) {
        bizChatConversationService.createConversation(conversation);
    }
}