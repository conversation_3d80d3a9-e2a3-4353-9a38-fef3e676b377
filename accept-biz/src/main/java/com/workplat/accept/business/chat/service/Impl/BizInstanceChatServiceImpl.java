package com.workplat.accept.business.chat.service.Impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.Header;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.constant.NodeStatusEnum;
import com.workplat.accept.business.chat.convert.BizInstanceChatVOConvert;
import com.workplat.accept.business.chat.dto.BizInstanceChatDTO;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.service.BizChatConversationService;
import com.workplat.accept.business.chat.service.BizInstanceChatService;
import com.workplat.accept.business.chat.vo.BizInstanceChatVO;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.gss.application.dubbo.constant.InstanceApplicationStatusEnum;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BizInstanceChatServiceImpl extends BaseServiceImpl<BizInstanceChat> implements BizInstanceChatService {

    @Autowired
    private BizInstanceChatVOConvert bizInstanceChatVOConvert;
    @Autowired
    private BizChatConversationService bizChatConversationService;
    @Autowired
    private BizInstanceInfoService bizInstanceInfoService;

    /**
     * 获取办件记录 三部分：本地办件记录 + allinone办件记录+ 对话记录产生的办件记录
     * @param dto
     * @return {@link List }<{@link BizInstanceChatVO }>
     */
    @Override
    public List<BizInstanceChatVO> getList(BizInstanceChatDTO dto) {
        // 1.数据一：查询本地办件记录
        JSONObject userJson = new JSONObject();
        if (StringUtils.isNotBlank(dto.getToken()) && dto.getToken().contains("test")) {
            userJson.put("certificateNumber", "123456");
            userJson.put("id", "yangfan");

            Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", "610431199905063016").put("name", "杨帆").build();
            String token = JSONUtil.parseObj(GatewayUtils.getAllinoneApiResult("/api/zzj/selfServiceLogin", formMap)).getStr("token");
            dto.setToken("Bearer " + token);
        } else {
            String responseString = GatewayUtils.executeGetRequest("https://zwfw.tcsjj.cn/gateway-api/allinone-api/api/ucenter/user/get",
                    ImmutableMap.of(Header.AUTHORIZATION.getValue(), dto.getToken()),
                    null, false, false);
            if (responseString == null) {
                return null;
            }
            userJson = JSONObject.parseObject(responseString);
        }
        String certificateNumber = userJson.getString("certificateNumber");
        if (StringUtils.isBlank(certificateNumber)) {
            return null;
        }
        Map<String, Object> localParams = new HashMap<>();
        localParams.put("=(instance.applicationCertificateCode)", certificateNumber);
        if (StringUtils.isNotBlank(dto.getStatus()) && !dto.getStatus().equals("all")) {
            localParams.put("=(currentNode)", dto.getStatus().toUpperCase());
        }
        Pageable pageable = PageRequest.of(0, 3, Sort.by(Sort.Direction.DESC, "createTime"));

        List<BizInstanceChat> list = queryForPage(localParams, pageable).getContent();
        List<BizInstanceChatVO> convert = bizInstanceChatVOConvert.convert(list);
        // 2.数据二：查询allinone办件记录 start 全部all  草稿draft_accept  办理中business_accepting  退回reject_business  完成finish_accept
        Map<String, Object> params = new HashMap<>();
        params.put("status", StringUtils.isBlank(dto.getStatus()) ? "all" : dto.getStatus());
        params.put("page", 1);
        params.put("limit", 4);
        params.put("sort", "businessTime,desc");
        String executeRequest = GatewayUtils.executeGetRequest("https://zwfw.tcsjj.cn/gateway-api/allinone-api/api/aioBusiness/findPage",
                ImmutableMap.of(Header.AUTHORIZATION.getValue(), dto.getToken()),
                params, false, false);
        JSONObject jsonObject = JSONObject.parseObject(executeRequest);
        if (jsonObject == null || jsonObject.getJSONArray("content") == null) {
            return convert;
        }
        // 转换追加办件记录
        for (Object o : jsonObject.getJSONArray("content")) {
            BizInstanceChatVO bizInstanceChatVO = ConvertToAllInone((JSONObject) o);
            convert.add(bizInstanceChatVO);
        }
        // 3.数据三：查询对话记录产生的办件记录
        if (dto.getStatus().equals("all") || dto.getStatus().equals("draft_accept")) {
            ImmutableMap<String, Object> chatParams = ImmutableMap.of("=(userId)", userJson.getString("id"),
                    "<>(instanceId)", "-1");
            Pageable pageable1 = PageRequest.of(0, 3, Sort.by(Sort.Direction.DESC, "updateTime"));
            Page<BizChatConversation> bizChatConversations = bizChatConversationService.queryForPage(chatParams, pageable1);
            bizChatConversations.getContent().forEach(bizChatConversation -> {
                BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(bizChatConversation.getInstanceId());
                if (InstanceApplicationStatusEnum.TEMP.equals(bizInstanceInfo.getApplicationStatusEnum())) {
                    BizInstanceChatVO bizInstanceChatVO = new BizInstanceChatVO();
                    bizInstanceChatVO.setId(bizChatConversation.getId());
                    bizInstanceChatVO.setInstanceId(bizChatConversation.getInstanceId());
                    bizInstanceChatVO.setCurrentNode(NodeStatusEnum.DRAFT_ACCEPT.name());
                    bizInstanceChatVO.setCurrentNodeName(NodeStatusEnum.DRAFT_ACCEPT.getValue());
                    bizInstanceChatVO.setName(bizInstanceInfo.getMatterName());
                    bizInstanceChatVO.setTime(bizInstanceInfo.getCreateTime());
                    bizInstanceChatVO.setUserName(bizInstanceInfo.getApplicationName());
                    bizInstanceChatVO.setChannel("chat");
                    convert.add(bizInstanceChatVO);
                }
            });
        }
        // 4.根据时间降序排序
        convert.sort(Comparator.comparing(BizInstanceChatVO::getTime).reversed());
        return convert;
    }

    @NotNull
    private static BizInstanceChatVO ConvertToAllInone(JSONObject o) {

        BizInstanceChatVO bizInstanceChatVO = new BizInstanceChatVO();
        bizInstanceChatVO.setCurrentNode(o.getString("status"));
        bizInstanceChatVO.setCurrentNodeName(o.getString("status"));
        bizInstanceChatVO.setNo(o.getString("businessNo"));
        bizInstanceChatVO.setName(o.getString("businessName"));
        bizInstanceChatVO.setTime(o.getDate("businessTime"));
        bizInstanceChatVO.setUserName(o.getString("businessObjectName"));
        bizInstanceChatVO.setChannel("allinone");
        bizInstanceChatVO.setSkipUrl(o.getString("goToUrl"));
        bizInstanceChatVO.setMobileUrl(o.getString("goToAPPUrl"));
        return bizInstanceChatVO;
    }
}
