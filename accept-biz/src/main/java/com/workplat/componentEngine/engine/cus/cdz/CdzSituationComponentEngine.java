package com.workplat.componentEngine.engine.cus.cdz;

import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.SituationComponentEngine;
import com.workplat.componentEngine.engine.content.InstructionConstant;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.api.api.BizInstanceSituationApi;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.entity.BizInstanceQuota;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceQuotaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 情境组件引擎
 *
 * <AUTHOR>
 * @date 2025/05/13
 */
@Slf4j
@Service
public class CdzSituationComponentEngine extends SituationComponentEngine {

    private final BizInstanceInfoService bizInstanceInfoService;
    private final BizInstanceQuotaService bizInstanceQuotaService;


    CdzSituationComponentEngine(BizInstanceSituationApi bizInstanceSituationApi,
                                BizInstanceInfoService bizInstanceInfoService,
                                BizInstanceQuotaService bizInstanceQuotaService) {
        super(bizInstanceSituationApi);
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.bizInstanceQuotaService = bizInstanceQuotaService;
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        log.info("CdzSituationComponentEngine fillData");
        super.fillData(componentDataContext);
    }

    @Override
    protected ComponentRunVO doExecute() {
        log.info("CdzSituationComponentEngine execute");
        return super.doExecute();
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(context.getInstanceId());
        return super.canHandle(context) && "cdzbgyjs".equals(bizInstanceInfo.getMatterCode());
    }


    @Override
    public String getNextInstruction(ComponentDataContext componentDataContext) {
        BizInstanceQuota quota = bizInstanceQuotaService
                .queryForSingle(ImmutableMap.of("=(instance.id)", componentDataContext.getInstanceId(), "=(title)", "车位性质"));
        if (!quota.getOptions().isEmpty() && "租赁车位".equals(quota.getOptions().getFirst().getLabel())) {
            return InstructionConstant.SUCCESS_NEXT.getCode();
        }
        return super.getNextInstruction(componentDataContext);
    }
}
