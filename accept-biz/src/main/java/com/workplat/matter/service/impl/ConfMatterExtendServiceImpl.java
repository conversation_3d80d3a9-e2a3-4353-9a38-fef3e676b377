package com.workplat.matter.service.impl;

import cn.hutool.core.map.MapUtil;
import com.workplat.gss.common.core.service.impl.BaseServiceImpl;
import com.workplat.matter.converter.ConfMatterExtendConverter;
import com.workplat.matter.entity.ConfMatterExtend;
import com.workplat.matter.service.ConfMatterExtendService;
import com.workplat.matter.vo.ConfMatterExtendVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.service.impl
 * @description 事项扩展信息ServiceImpl
 * @date 2025/5/20 14:05
 */
@Service
public class ConfMatterExtendServiceImpl extends BaseServiceImpl<ConfMatterExtend> implements ConfMatterExtendService {

    @Autowired
    private ConfMatterExtendConverter confMatterExtendConverter;

    @Override
    public ConfMatterExtend findByMatterId(String matterId) {
        return this.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.id)", matterId)
                .build());
    }

    @Override
    public ConfMatterExtendVO findVOByMatterId(String matterId) {
        return confMatterExtendConverter.convert(findByMatterId(matterId));
    }
}
