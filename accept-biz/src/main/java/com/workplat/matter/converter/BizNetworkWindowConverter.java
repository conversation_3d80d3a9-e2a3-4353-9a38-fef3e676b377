package com.workplat.matter.converter;

import com.workplat.gss.common.core.converter.BaseConverter;
import com.workplat.matter.entity.BizNetworkWindow;
import com.workplat.matter.dto.NetworkWindowDTO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.converter
 * @description
 * @date 2025/6/10 14:24
 */
@Component
public class BizNetworkWindowConverter implements BaseConverter<BizNetworkWindow, NetworkWindowDTO> {

}
