//package com.workplat.gss.script.biz.loader;
package com.workplat.electronic.certificate;

import com.google.common.collect.ImmutableMap;
import com.workplat.gss.application.dubbo.entity.BizInstanceQuota;
import com.workplat.gss.application.dubbo.service.BizInstanceQuotaService;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoDetailVO;
import com.workplat.gss.common.script.service.declare.DistributionDriverLoader;
import com.workplat.gss.common.script.model.declare.DistributionDriverInput;
import com.workplat.gss.common.script.model.declare.DistributionDriverOutput;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.push.power.service.MattersDeclareService;

/**
 * 劳务派遣新办办件推送
 */
public class LaoWuXbDistributionDriverLoaderImpl implements DistributionDriverLoader {


    @Override
    public DistributionDriverOutput distributionDriver(DistributionDriverInput input) {
        DistributionDriverOutput output = new DistributionDriverOutput();
        JSONObject oo = new JSONObject();
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceInfoDetailVO detailVOById = bizInstanceInfoService.getDetailVOById(input.getInstanceId());


        // 获取办理区域
        BizInstanceQuotaService bizInstanceQuotaService = ApplicationContextUtil.getBean(BizInstanceQuotaService.class);
        BizInstanceQuota quota = bizInstanceQuotaService.queryForSingle(ImmutableMap.of("=(instance.id)", input.getInstanceId(),
                "=(title)", "请您根据单位营业执照上的注册地址选择对应的区域"));
        String optionValue = quota.getOptions().getFirst().getLabel();
        // 获取任务id
        MattersDeclareService mattersDeclareService = ApplicationContextUtil.getBean(MattersDeclareService.class);

        JSONObject itemInfoJson = JSONObject.parseObject(getItemInfo(optionValue));
        mattersDeclareService.executed(itemInfoJson, detailVOById);
        return output;
    }

    String getItemInfo(String area) {
        // materials 受理平台的事项材料ID：AI中事项材料编码
        return switch (area) {
            case "浏河镇" -> """
                    {
                      "taskId": "0223638c0000000067190e8d00001329",
                      "materials": {
                        "0223638c0000000067436f4e00001375": "lwpqjyxksqs",
                        "0223638c000000006742d37b00001376": "jylwpaywzkgzcns",
                        "0223638c000000006742fef900001377": "0223638c000000006742fef900001377",
                        "0223638c000000006744218100001378": "0223638c000000006744218100001372,0223638c000000006744218100001371,0223638c000000006744218100001378",
                        "0223638c000000006744560900001379": "sqblxzxksxwts"
                      }
                    }""";
            case "港区", "浮桥镇" -> """
                    {
                      "taskId": "0223638c0000000065dfca6f00001231",
                      "materials": {
                        "0223638c0000000065e0fa5300001244": "lwpqjyxksqs",
                        "0223638c0000000065e2641700001247": "jylwpaywzkgzcns",
                        "0223638c0000000065e19b9600001248": "0223638c000000006742fef900001377",
                        "0223638c0000000065e1e2e700001249": "0223638c000000006744218100001372,0223638c000000006744218100001371,0223638c000000006744218100001378",
                        "0223638c0000000065e237ef00001245": "sqblxzxksxwts"
                      }
                    }""";
            default -> """
                    {
                      "taskId": "0223638c000000006f45f507ffffa8f6",
                      "materials": {
                        "0223638c000000006fb551bd00000074": "lwpqjyxksqs",
                        "0223638c000000006fb4ba8f00000075": "jylwpaywzkgzcns",
                        "0223638c000000006fb5c0af00000077": "0223638c000000006742fef900001377",
                        "0223638c000000006fb7116a00000078": "0223638c000000006744218100001372,0223638c000000006744218100001371,0223638c000000006744218100001378",
                        "0223638c000000006fb7434100000079": "sqblxzxksxwts"
                      }
                    }""";
        };
    }
}