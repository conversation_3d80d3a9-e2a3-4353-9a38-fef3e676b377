//package com.workplat.gss.script.biz.loader;
package com.workplat.electronic.certificate;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.constant.NodeStatusEnum;
import com.workplat.accept.business.chat.entity.BizInstanceChat;
import com.workplat.accept.business.chat.service.BizInstanceChatService;
import com.workplat.gss.application.dubbo.entity.BizInstanceQuota;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceQuotaService;
import com.workplat.gss.application.dubbo.vo.BizInstanceInfoDetailVO;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.script.model.declare.DistributionDriverInput;
import com.workplat.gss.common.script.model.declare.DistributionDriverOutput;
import com.workplat.gss.common.script.service.declare.DistributionDriverLoader;
import com.workplat.push.ga.service.GaDeclarePushService;
import com.workplat.push.power.service.MattersDeclareService;

/**
 * 劳务派遣新办办件推送
 */
public class GaDistributionDriverLoaderImpl implements DistributionDriverLoader {


    @Override
    public DistributionDriverOutput distributionDriver(DistributionDriverInput input) {
        DistributionDriverOutput output = new DistributionDriverOutput();
        JSONObject oo = new JSONObject();
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        GaDeclarePushService gaDeclarePushService = ApplicationContextUtil.getBean(GaDeclarePushService.class);

        gaDeclarePushService.push(input.getInstanceId(), true);
        // 保存一条会话办件记录
        BizInstanceChatService bizInstanceChatService = ApplicationContextUtil.getBean(BizInstanceChatService.class);

        BizInstanceChat bizInstanceChat = new BizInstanceChat();
        bizInstanceChat.setInstance(bizInstanceInfoService.queryById(input.getInstanceId()));
        bizInstanceChat.setCurrentNode(NodeStatusEnum.NO_ACCEPT.name());
        bizInstanceChat.setCurrentNodeName(NodeStatusEnum.NO_ACCEPT.getValue());
        bizInstanceChatService.create(bizInstanceChat);
        return output;
    }

}