package com.workplat.accept.business.chat.api;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.workplat.accept.business.chat.dto.*;
import com.workplat.accept.business.chat.vo.BizChatConversationVO;
import com.workplat.accept.business.chat.vo.BizChatMessageVO;
import com.workplat.accept.business.chat.vo.BizInstanceChatVO;
import com.workplat.componentEngine.dto.ComponentRunDTO;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Page;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;

@Tag(name = "对话")
@RestController
@RequestMapping(value = "/api/chat")
public interface ChatApi {

    @Operation(summary = "组件运行")
    @PostMapping("/component/run")
    ResponseData<ComponentRunVO> componentRun(@RequestBody ComponentRunDTO componentRunDto);

    @Operation(summary = "问答")
    @PostMapping("/ask")
    Flux<ServerSentEvent<String>> ask(@RequestBody AskDTO ask) throws JsonProcessingException;

    @Operation(summary = "问答阻塞式")
    @PostMapping("/ask/block")
    ResponseData<Object> askBlock(@RequestBody AskDTO ask) throws JsonProcessingException;

    @Operation(summary = "用户历史会话分页")
    @GetMapping("/conversationPage")
    ResponseData<Page<BizChatConversationVO>> getConversationPage(@Validated BizChatConversationDTO dto, PageableDTO pageable);

    @Operation(summary = "用户历史会话列表删除")
    @PostMapping("/conversation/delete")
    ResponseData<Void> deleteConversation(@RequestBody DeleteConversationDTO deleteConversationDTO);

    @Operation(summary = "获取最新的一个会话(近2小时)")
    @GetMapping("/conversation/latest")
    ResponseData<BizChatConversationVO> getLatestConversation(@Validated BizChatConversationDTO dto);

    @Operation(summary = "用户历史会话详情")
    @GetMapping("/conversation/messages")
    ResponseData<List<BizChatMessageVO>> getConversationMessages(BizChatMessageDTO dto);

    @Operation(summary = "更新文件上传列表")
    @PostMapping("/file/upload")
    ResponseData<Void> updateFileUploadList(@RequestBody UpdateFileUploadDTO updateFileUploadDTO);

    @Operation(summary = "停止对话")
    @PostMapping("/stop")
    ResponseData<Void> stopChat(@RequestBody StopChatDTO stopChatDTO);

    @Operation(summary = "记录一个对话")
    @PostMapping("/record")
    ResponseData<Void> recordChat(@RequestBody BizChatMessageDTO dto);

    @Operation(summary = "文件字段内容提取-百炼")
    @PostMapping(value = "/file/withdrawBl")
    ResponseData<Object> withdrawBl(@RequestBody FileClassifyDTO dto);

    @Operation(summary = "提交签名")
    @PostMapping("/commitSign")
    ResponseData<Void> commitSign(@RequestBody CommitSignDTO dto);

    @Operation(summary = "办件列表")
    @GetMapping("/bizInstance/list")
    ResponseData<List<BizInstanceChatVO>> bizInstanceList(HttpServletRequest request, BizInstanceChatDTO dto);
}
