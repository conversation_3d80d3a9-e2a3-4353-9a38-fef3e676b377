package com.workplat.accept.business.accept.api;

import com.workplat.gss.common.core.response.ResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Odin
 * @Date: 2024/9/23 09:53
 * @Description:
 */

@Tag(name = "业务审批")
@RestController
@RequestMapping("/api/instance/accept")
public interface InstanceAcceptApi {

    @Operation(summary = "更改办件状态为草稿")
    @GetMapping("/updateDraft")
    ResponseData<String> updateDraft(String instanceId);

    @Operation(summary = "重新提交")
    @GetMapping("/reSubmit")
    ResponseData<String> reSubmit(String instanceId);
}
