package com.workplat.matter.api;

import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.matter.dto.ConfMatterExtendDTO;
import com.workplat.matter.vo.ConfMatterExtendVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description 事项信息Api
 * @date 2025/5/21 16:11
 */
@Validated
@RestController
@RequestMapping("/api/matter/blbb/")
@Tag(name = "边聊边办事项信息模块")
public interface MatterInfoApi {

    @GetMapping("/getByName")
    @Operation(summary = "通过名称获取事项信息")
    ResponseData<ConfMatterExtendVO> getByName(@NotBlank(message = "名称不能为空") String name);

    @GetMapping("/getByMatterId")
    @Operation(summary = "通过事项id获取扩展信息")
    ResponseData<ConfMatterExtendVO> getByMatterId(String matterId);

    @GetMapping("/getByMatterCode")
    @Operation(summary = "通过事项code获取扩展信息")
    ResponseData<ConfMatterExtendVO> getByMatterCode(String matterCode);

    @PostMapping("/saveExtend")
    @Operation(summary = "保存事项扩展信息")
    ResponseData<Void> saveExtend(@RequestBody ConfMatterExtendDTO dto);

    @GetMapping("/queryItemPage")
    @Operation(summary = "分页查询边聊边办事项清单")
    ResponseData<Page<ConfMatterExtendVO>> queryItemPage(PageableDTO pageDTO, String keyword);
}
