package com.workplat.matter.api;

import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.matter.dto.QuestionRecommendDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description
 * @date 2025/5/27 13:24
 */
@Validated
@RestController
@RequestMapping("/api/matter/question")
@Tag(name = "边聊边办问题推荐模块")
public interface QuestionRecommendApi {

    @PostMapping("/save")
    @Operation(summary = "保存推荐")
    ResponseData<QuestionRecommendDTO> save(@Valid @RequestBody QuestionRecommendDTO dto);

    @GetMapping("/page")
    @Operation(summary = "获取推荐列表")
    ResponseData<Page<QuestionRecommendDTO>> page(PageableDTO pageDTO, String type);

    @GetMapping("/delete")
    @Operation(summary = "删除推荐")
    ResponseData<Void> delete(String id);

    @GetMapping("/getById")
    @Operation(summary = "通过id获取推荐")
    ResponseData<QuestionRecommendDTO> getById(String id);

    @GetMapping("/questionList")
    @Operation(summary = "问题推荐清单")
    ResponseData<Map<String, List<String>>> questionList();
}
